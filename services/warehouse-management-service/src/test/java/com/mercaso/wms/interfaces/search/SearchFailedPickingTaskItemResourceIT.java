package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.createBatch;
import com.mercaso.wms.utils.PickingTaskResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;

class SearchFailedPickingTaskItemResourceIT extends AbstractIT {

    @Autowired
    PickingTaskResourceApi pickingTaskResourceApi;

    @Autowired
    PickingTaskRepository pickingTaskRepository;

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Autowired
    private PickingTaskApplicationEventListener listener;

    @Test
    void when_search_failed_picking_task_items_then_return_data() throws Exception {
        pickingTaskRepository.deleteAll();
        String orderNumber = RandomStringUtils.randomAlphabetic(10);

        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setName(orderNumber);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 1);
        batchItems.forEach(batchItem -> batchItem.setOrderNumber(orderNumber));
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);
        listener.handleBatchCreatedEvent(saved.getId());

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        pickingTasks.getFirst().getPickingTaskItems().getFirst().setErrorInfo("error");
        pickingTasks.getFirst().setState(PickingTaskStatus.FAILED);

        pickingTaskRepository.save(pickingTasks.getFirst());

        Result<FailedPickingTaskItemDto> pickingTaskItemDtos = pickingTaskResourceApi.searchFailedPickingTaskItems(saved.getTag(),
            List.of(pickingTasks.getFirst().getNumber()), SourceEnum.MDC.name());

        assertEquals(1, pickingTaskItemDtos.getData().size());
        assertEquals("error", pickingTaskItemDtos.getData().getFirst().getErrorInfo());
    }
}