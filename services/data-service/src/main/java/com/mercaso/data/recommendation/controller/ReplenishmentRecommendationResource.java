package com.mercaso.data.recommendation.controller;

import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/recommendation/v1/replenishment-recommendations")
@RequiredArgsConstructor
public class ReplenishmentRecommendationResource {

  // The mock logic will be removed when the real data is available
  private static final List<DepartmentDto> MOCK_DEPARTMENTS = List.of(
      new DepartmentDto("660e8400-e29b-41d4-a716-************", "Fruits & Vegetables"),
      new DepartmentDto("660e8400-e29b-41d4-a716-************", "Dairy & Eggs"),
      new DepartmentDto("660e8400-e29b-41d4-a716-************", "Meat & Seafood"),
      new DepartmentDto("660e8400-e29b-41d4-a716-************", "Bakery"),
      new DepartmentDto("660e8400-e29b-41d4-a716-************", "Beverages")
  );

  @GetMapping("/departments")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public List<DepartmentDto> getDepartments(@RequestParam("storeId") String storeId) {
    log.info("Get departments for store: {}", storeId);
    return MOCK_DEPARTMENTS;
  }

  @GetMapping("/search")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public PageableResponse<ReplenishmentRecommendationDto> search(
      @RequestParam(defaultValue = "0") Integer pageNumber,
      @RequestParam(defaultValue = "20") Integer pageSize,
      @RequestParam String storeId,
      @RequestParam(required = false) String departmentId) {

    log.info(
        "Search replenishment recommendations for store: {}, departmentId: {}, page: {}, size: {}",
        storeId, departmentId, pageNumber, pageSize);

    List<ReplenishmentRecommendationDto> mockData = createMockData();

    if (departmentId != null && !departmentId.trim().isEmpty()) {
      UUID departmentUuid = UUID.fromString(departmentId);
      mockData = mockData.stream()
          .filter(dto -> departmentUuid.equals(dto.getDepartmentId()))
          .toList();
    }

    int startIndex = pageNumber * pageSize;
    int endIndex = Math.min(startIndex + pageSize, mockData.size());

    List<ReplenishmentRecommendationDto> pageData = startIndex < mockData.size()
        ? mockData.subList(startIndex, endIndex)
        : new ArrayList<>();

    int totalPages = (int) Math.ceil((double) mockData.size() / pageSize);

    return PageableResponse.<ReplenishmentRecommendationDto>builder()
        .data(pageData)
        .pageNumber(pageNumber)
        .pageSize(pageSize)
        .totalPages(totalPages)
        .totalElements(mockData.size())
        .build();
  }

  private List<ReplenishmentRecommendationDto> createMockData() {
    List<ReplenishmentRecommendationDto> mockData = new ArrayList<>();

    for (int i = 1; i <= 50; i++) {

      ReplenishmentRecommendationDto dto = ReplenishmentRecommendationDto.builder()
          .storeId(UUID.fromString("550e8400-e29b-41d4-a716-************"))
          .sku("SKU" + String.format("%03d", i))
          .name("Product " + i)
          .recommendedQuantity(BigDecimal.valueOf(10 + (i % 20) * 5))
          .nextOrderTime(Instant.now().plusSeconds(86400L * (i % 7)))
          .batchNumber("BATCH_" + (2024000 + i))
          .upc("123456789" + String.format("%03d", i))
          .productId("PROD_" + i)
          .lastPurchaseTime(Instant.now().minusSeconds(86400L * (i % 30)))
          .lastPurchaseQuantity(5 + (i % 15))
          .departmentId(UUID.fromString(MOCK_DEPARTMENTS.get((i - 1) % 5).id()))
          .departmentName(MOCK_DEPARTMENTS.get((i - 1) % 5).name())
          .build();

      mockData.add(dto);
    }

    return mockData;
  }
}
