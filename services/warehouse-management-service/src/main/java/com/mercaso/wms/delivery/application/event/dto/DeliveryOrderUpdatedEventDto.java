package com.mercaso.wms.delivery.application.event.dto;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderUpdatedEventDto extends BaseDto {

    private UUID deliveryOrderId;

    private UpdateDeliveryOrder source;

    private UpdateDeliveryOrder target;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UpdateDeliveryOrder implements Serializable {

        private RescheduleType rescheduleType;
    }

}
