package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MasterCatalogRawDataDuplicationRepository extends JpaRepository<MasterCatalogRawDataDuplication, UUID> {

    List<MasterCatalogRawDataDuplication> findAllByDuplicationGroupIn(Collection<UUID> duplicationGroups);

    @Query("""
        select duplication from MasterCatalogRawDataDuplication as duplication
        where duplication.duplicationGroup in (
          select mcrd.duplicationGroup from MasterCatalogRawDataDuplication mcrd
          where mcrd.upc in (:upcs)
        )
        """)
    List<MasterCatalogRawDataDuplication> findAllUnderTheSameGroupByUpcIn(List<String> upcs);

    List<MasterCatalogRawDataDuplication> findAllByUpcIn(List<String> upcs);

    List<MasterCatalogRawDataDuplication> findAllByIdIn(Collection<UUID> masterCatalogRawDataIds);

    @Query(value = """
        SELECT DISTINCT d.* 
        FROM master_catalog_raw_data_duplication d
        CROSS JOIN LATERAL jsonb_array_elements(d.reasons) AS reason
        WHERE reason->>'duplicateValue' IN (:duplicateValues)
        /*{dsl.optionalIndexHint}*/ 
        """,
        nativeQuery = true)
    List<MasterCatalogRawDataDuplication> findAllWithDuplicateValues(@Param("duplicateValues") Set<String> duplicateValues);

    @Query("SELECT duplicationGroup FROM MasterCatalogRawDataDuplication WHERE duplicationGroup IN :groups GROUP BY duplicationGroup ORDER BY MIN(createdAt) ASC LIMIT 1")
    Optional<UUID> findOldestGroup(@Param("groups") Set<UUID> groups);

    @Query(value = """
        SELECT d.duplication_group
        FROM master_catalog_raw_data_duplication d
        CROSS JOIN LATERAL jsonb_array_elements(d.reasons) AS reason
        WHERE reason->>'duplicateField' = :duplicateField
        AND reason->>'duplicateValue' = :duplicateValue
        LIMIT 1
        """, nativeQuery = true)
    Optional<UUID> findGroupIdByDuplicateValue(@Param("duplicateField") String duplicateField,
        @Param("duplicateValue") String duplicateValue);

    List<MasterCatalogRawDataDuplication> findByUpc(String upc);

    @Query("SELECT upc from MasterCatalogRawDataDuplication where duplicationGroup in :duplicationGroupLIds")
    List<String> findUpcsByDuplicationGroupIn(List<UUID> duplicationGroupLIds);
}
