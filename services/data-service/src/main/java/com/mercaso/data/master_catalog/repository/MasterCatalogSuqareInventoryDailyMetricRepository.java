package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogSuqareInventoryDailyMetricRepository extends
    JpaRepository<MasterCatalogSuqareInventoryDailyMetric, Long> {

    @Query("SELECT m FROM MasterCatalogSuqareInventoryDailyMetric m " +
        "WHERE m.masterCatalogRawDataId = :masterCatalogRawDataId " +
        "AND m.date BETWEEN :startDate AND :endDate " +
        "AND NOT (m.quantity = 0 AND m.inStockQuantity = 0) " +
        "ORDER BY m.date DESC")
    List<MasterCatalogSuqareInventoryDailyMetric> findByDateRangeAndNotBothQuantitiesZero(
        @Param("masterCatalogRawDataId") UUID masterCatalogRawDataId,
        @Param("startDate") Instant startDate,
        @Param("endDate") Instant endDate);
}
