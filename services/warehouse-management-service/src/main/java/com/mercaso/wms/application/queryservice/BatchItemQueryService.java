package com.mercaso.wms.application.queryservice;

import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class BatchItemQueryService {

    private final BatchItemRepository batchItemRepository;

    public List<BatchItem> findBigBeverageBatchItemsByBatchId(UUID batchId, String source) {
        return batchItemRepository.findBatchItemsBy(batchId,
            source,
            List.of(BatchConstants.BEVERAGE, BatchConstants.CLEANING_AND_LAUNDRY),
            true);
    }

    public List<BatchItem> findSmallBeverageBatchItemsByBatchId(UUID batchId, String source) {
        return batchItemRepository.findBatchItemsBy(batchId,
            source,
            List.of(BatchConstants.BEVERAGE, BatchConstants.CLEANING_AND_LAUNDRY),
            false);
    }

    public List<BatchItem> findCandyBatchItemsByBatchId(UUID batchId, String source) {
        return batchItemRepository.findBatchItemsByDepartmentNotIn(batchId,
            source,
            List.of(BatchConstants.BEVERAGE, BatchConstants.CLEANING_AND_LAUNDRY));
    }

    public List<BatchItem> findBy(UUID batchId, String source) {
        return batchItemRepository.findBatchItemsBy(batchId, source);
    }
    public List<BatchItem> findBy(UUID batchId, List<String> existingOrderNumbers) {
        return batchItemRepository.findBatchItemsBy(batchId, existingOrderNumbers);
    }

    public Page<BatchItem> searchBatchItems(String source, String deliveryDate, Pageable page) {
        return batchItemRepository.searchBatchItems(source, deliveryDate, page);
    }

    public List<BatchItem> findByBatchIdAndSourceAndLocationName(UUID batchId, String source, String locationName) {
        return batchItemRepository.findByBatchIdAndSourceAndLocationName(batchId, source, locationName);
    }

    public BatchItem findById(UUID id) {
        return batchItemRepository.findById(id);
    }

    public List<BatchItem> findBigOrdersByBatchIdAndSource(UUID batchId, String source) {
        return batchItemRepository.findBatchItemsBy(batchId, source, true);
    }

}
