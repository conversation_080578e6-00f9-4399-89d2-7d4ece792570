package com.mercaso.wms.delivery.application.service;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.queryservice.DeliveryOrderQueryService;
import com.mercaso.wms.delivery.application.queryservice.DocumentQueryService;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.listener.DeliveryOrderApplicationEventListener;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class CustomerInvoiceService {

    private final DeliveryOrderApplicationEventListener deliveryOrderApplicationEventListener;
    private final DeliveryOrderQueryService deliveryOrderQueryService;
    private final DocumentQueryService documentQueryService;

    public String createCustomerInvoice(UUID deliveryOrderId) {

        List<DocumentDto> documents = documentQueryService.findItemDocumentsBy(deliveryOrderId);
        boolean hasInvoice = CollectionUtils.isNotEmpty(documents) && documents.stream()
            .anyMatch(d -> d.getDocumentType() == DocumentType.INVOICE);

        if (hasInvoice) {
            throw new DeliveryBadRequestException(
                "Customer invoice already exists for delivery order: " + deliveryOrderId
            );
        }

        DeliveryOrderDto deliveryOrderDto = deliveryOrderQueryService.findById(deliveryOrderId);
        try {
            DocumentDto generated = deliveryOrderApplicationEventListener.generate(deliveryOrderDto);
            return generated.getFileUrl();
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            throw new DeliveryBusinessException("Error when generate customer invoice", e);
        }
    }
}
