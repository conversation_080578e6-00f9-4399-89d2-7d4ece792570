package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VernonItemDailyUpdatedData {

    @ExcelProperty("itemno #")
    private String itemNumber;
    @ExcelProperty("desc1")
    private String description1;
    @ExcelProperty("picfile1")
    private String picFile1;
    @ExcelProperty("qtcase")
    private Integer qtCase;
    @ExcelProperty("qtpack")
    private Integer qtPack;
    @ExcelProperty("qtpall")
    private Integer qtPall;
    @ExcelProperty("price1")
    private BigDecimal price1;
    @ExcelProperty("price5")
    private BigDecimal price5;
    @ExcelProperty("upcno")
    private String upcNo;
    @ExcelProperty("wtcase")
    private String wtCase;
    @ExcelProperty("vocase")
    private String voCase;
    @ExcelProperty("dept")
    private String dept;
    @ExcelProperty("categ")
    private String categ;
    @ExcelProperty("origin")
    private String origin;

}
