package com.mercaso.ims.application.searchservice;

import com.mercaso.ims.application.dto.CategoryItemCountsDto;
import com.mercaso.ims.application.dto.ItemListCustomFilterKeyDto;
import com.mercaso.ims.application.dto.ItemListDto;
import com.mercaso.ims.application.query.ItemQuery;
import java.util.List;
import java.util.UUID;


public interface ItemSearchApplicationService {

    ItemListDto searchItemListV2(ItemQuery itemQuery);

    List<ItemListCustomFilterKeyDto> searchItemsListCustomFilter();

    List<CategoryItemCountsDto> countItemsByCategoryIdAndStatus(List<UUID> categoryIds);

    List<UUID> searchItemListIds(ItemQuery itemQuery);

    Long searchItemCount(ItemQuery itemQuery);
}
