package com.mercaso.wms.delivery.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.queryservice.DeliveryOrderQueryService;
import com.mercaso.wms.delivery.application.queryservice.DocumentQueryService;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.listener.DeliveryOrderApplicationEventListener;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CustomerInvoiceServiceTest {

    @Mock
    private DeliveryOrderApplicationEventListener deliveryOrderApplicationEventListener;

    @Mock
    private DeliveryOrderQueryService deliveryOrderQueryService;

    @Mock
    private DocumentQueryService documentQueryService;

    @InjectMocks
    private CustomerInvoiceService customerInvoiceService;

    private UUID deliveryOrderId;
    private DeliveryOrderDto deliveryOrderDto;
    private DocumentDto documentDto;

    @BeforeEach
    void setUp() {
        deliveryOrderId = UUID.randomUUID();
        deliveryOrderDto = mock(DeliveryOrderDto.class);
        documentDto = mock(DocumentDto.class);
    }

    @Test
    void when_createCustomerInvoice_with_no_existing_invoice_then_return_file_url() throws IOException, InterruptedException {
        // Given
        when(documentQueryService.findItemDocumentsBy(deliveryOrderId)).thenReturn(Collections.emptyList());
        when(deliveryOrderQueryService.findById(deliveryOrderId)).thenReturn(deliveryOrderDto);
        when(deliveryOrderApplicationEventListener.generate(deliveryOrderDto)).thenReturn(documentDto);
        when(documentDto.getFileUrl()).thenReturn("https://example.com/invoice.pdf");

        // When
        String fileUrl = customerInvoiceService.createCustomerInvoice(deliveryOrderId);

        // Then
        assertNotNull(fileUrl);
        assertEquals("https://example.com/invoice.pdf", fileUrl);
        verify(documentQueryService).findItemDocumentsBy(deliveryOrderId);
        verify(deliveryOrderQueryService).findById(deliveryOrderId);
        verify(deliveryOrderApplicationEventListener).generate(deliveryOrderDto);
    }

    @Test
    void when_createCustomerInvoice_with_existing_invoice_then_throw_DeliveryBadRequestException()
        throws IOException, InterruptedException {
        // Given
        DocumentDto existingDocument = mock(DocumentDto.class);
        when(existingDocument.getDocumentType()).thenReturn(DocumentType.INVOICE);
        when(documentQueryService.findItemDocumentsBy(deliveryOrderId)).thenReturn(List.of(existingDocument));

        // When & Then
        DeliveryBadRequestException exception = assertThrows(DeliveryBadRequestException.class, () ->
            customerInvoiceService.createCustomerInvoice(deliveryOrderId)
        );

        assertEquals("Customer invoice already exists for delivery order: " + deliveryOrderId, exception.getMessage());
        verify(documentQueryService).findItemDocumentsBy(deliveryOrderId);
        verify(deliveryOrderQueryService, never()).findById(any());
        verify(deliveryOrderApplicationEventListener, never()).generate(any());
    }

    @Test
    void when_createCustomerInvoice_with_generation_error_then_throw_DeliveryBusinessException() {
        // Given
        when(documentQueryService.findItemDocumentsBy(deliveryOrderId)).thenReturn(Collections.emptyList());
        when(deliveryOrderQueryService.findById(deliveryOrderId)).thenReturn(deliveryOrderDto);
        try {
            when(deliveryOrderApplicationEventListener.generate(deliveryOrderDto)).thenThrow(new IOException("Generation error"));
        } catch (IOException | InterruptedException e) {
            // Mock exception handling - this block should not execute during test
        }

        // When & Then
        DeliveryBusinessException exception = assertThrows(DeliveryBusinessException.class, () ->
            customerInvoiceService.createCustomerInvoice(deliveryOrderId)
        );

        assertEquals("Error when generate customer invoice", exception.getMessage());
        verify(documentQueryService).findItemDocumentsBy(deliveryOrderId);
        verify(deliveryOrderQueryService).findById(deliveryOrderId);
        try {
            verify(deliveryOrderApplicationEventListener).generate(deliveryOrderDto);
        } catch (IOException | InterruptedException e) {
            // Verification exception handling - this block should not execute during test
        }
    }
}