package com.mercaso.wms.delivery.interfaces;

import com.mercaso.wms.delivery.application.service.CustomerInvoiceService;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Customer Invoices")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/customer-invoices")
@RequiredArgsConstructor
public class CustomerInvoiceResource {

    private final CustomerInvoiceService customerInvoiceService;

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:customer-invoices')")
    @PostMapping("/{deliveryOrderId}")
    public String create(@PathVariable UUID deliveryOrderId) {

        log.info("Creating customer invoice for delivery order {}", deliveryOrderId);
        return customerInvoiceService.createCustomerInvoice(deliveryOrderId);
    }
}
