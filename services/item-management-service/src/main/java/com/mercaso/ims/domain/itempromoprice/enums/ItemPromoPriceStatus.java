package com.mercaso.ims.domain.itempromoprice.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.Arrays;

public enum ItemPromoPriceStatus {
    ACTIVE,
    DRAFT,
    UNKNOWN,
    ;

    @JsonCreator
    public static ItemPromoPriceStatus fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }
}
