package com.mercaso.wms.delivery.application.queryservice;

import com.mercaso.wms.delivery.application.dto.gps.GpsRawDto;
import com.mercaso.wms.delivery.application.dto.gps.LatestGpsCacheDto;
import com.mercaso.wms.delivery.application.mapper.gps.GpsRawDtoApplicationMapper;
import com.mercaso.wms.delivery.application.mapper.gps.LatestGpsCacheDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.gps.GpsRawRepository;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCacheRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GpsRawQueryService {

    private final GpsRawRepository gpsRawRepository;

    private final GpsRawDtoApplicationMapper gpsRawDtoApplicationMapper;

    private final LatestGpsCacheRepository latestGpsCacheRepository;

    private final LatestGpsCacheDtoApplicationMapper latestGpsCacheDtoApplicationMapper;

    public List<GpsRawDto> findByDeliveryTaskId(UUID id) {
        return gpsRawDtoApplicationMapper.domainToDtos(gpsRawRepository.findByDeliveryTaskId(id));
    }

    public List<LatestGpsCacheDto> findAllLatestGps() {
        return latestGpsCacheDtoApplicationMapper.domainToDtos(latestGpsCacheRepository.findAllByRecentHour());
    }

}
