package com.mercaso.wms.delivery.interfaces.query;

import com.mercaso.wms.delivery.application.dto.gps.GpsRawDto;
import com.mercaso.wms.delivery.application.dto.gps.LatestGpsCacheDto;
import com.mercaso.wms.delivery.application.queryservice.GpsRawQueryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "GPS Raw")
@Slf4j
@RestController
@RequestMapping("/delivery/query/gps-raw")
@RequiredArgsConstructor
public class QueryGpsRawResource {

    private final GpsRawQueryService gpsRawQueryService;

    @PreAuthorize("hasAuthority('da:read:gps-raw')")
    @GetMapping("/{deliveryTaskId}")
    public List<GpsRawDto> findByDeliveryTaskId(@PathVariable UUID deliveryTaskId) {
        return gpsRawQueryService.findByDeliveryTaskId(deliveryTaskId);
    }

    @PreAuthorize("hasAuthority('da:read:gps-raw')")
    @GetMapping("/latest-gps/list")
    public List<LatestGpsCacheDto> listLatestGps() {
        return gpsRawQueryService.findAllLatestGps();
    }

}
