package com.mercaso.wms.delivery.infrastructure.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.delivery.application.dto.customer.CustomerDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.AddressDetailData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.CustomerDetailData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.InvoiceLineItemData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.OrderNoteData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.OrganizationData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.SummaryData;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredApplicationEvent;
import com.mercaso.wms.delivery.application.service.DocumentApplicationService;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.document.Document;
import com.mercaso.wms.delivery.domain.document.DocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.OnesignalAdaptor;
import com.mercaso.wms.delivery.infrastructure.utils.GeneratePdfService;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeliveryOrderApplicationEventListener {

    private final GeneratePdfService generatePdfService;

    private final DocumentRepository documentRepository;

    private final DocumentApplicationService documentApplicationService;

    private final OnesignalAdaptor onesignalAdaptor;

    private final DeliveryTaskRepository deliveryTaskRepository;

    private final FeatureFlagsManager featureFlagsManager;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    public void handleDeliveredEvent(DeliveryOrderDeliveredApplicationEvent applicationEvent) {
        log.info("[handleDeliveredEvent] delivery order {} has been delivered",
            applicationEvent.getPayload().getDeliveryOrderId());
        try {
            DeliveryOrderDto deliveryOrderDto = applicationEvent.getPayload().getData();
            //generate invoice
            DocumentDto documentDto = generate(deliveryOrderDto);
            log.info("[handleDeliveredEvent] invoice generated for delivery order {} {}",
                deliveryOrderDto.getOrderNumber(),
                documentDto.getFileName());
            CustomerDto customer = deliveryOrderDto.getCustomer();
            if (customer == null || customer.getEmail() == null) {
                log.info("[handleDeliveredEvent] customer is null for delivery order {}", deliveryOrderDto.getOrderNumber());
                return;
            }
            if (featureFlagsManager.isFeatureOn(FeatureFlagKeys.SEND_INVOICE_EMAIL_TO_CUSTOMER)) {
                //send email
                onesignalAdaptor.sendEmail(
                    customer.getEmail(),
                    Map.of("orderNumber", deliveryOrderDto.getOrderNumber(),
                        "customerName", customer.getFirstName().concat(" ").concat(customer.getLastName()),
                        "invoiceUrl",
                        documentApplicationService.generateInvoiceSignatureWithExpiration(documentDto.getFileName()),
                        "deliveryDate",
                        deliveryOrderDto.getDeliveryDate(),
                        "totalAmount",
                        deliveryOrderDto.getTotalPrice() != null ? deliveryOrderDto.getTotalPrice().toString() : "0.0"));
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("[handleDeliveredEvent] error generating invoice", e);
        }
    }

    public DocumentDto generate(DeliveryOrderDto deliveryOrderDto) throws IOException, InterruptedException {
        GenerateInvoiceDto generateInvoiceDto = new GenerateInvoiceDto();
        generateInvoiceDto.setDeliveryOrderId(deliveryOrderDto.getId());
        generateInvoiceDto.setOrderNumber(deliveryOrderDto.getOrderNumber().replace("M-", ""));
        generateInvoiceDto.setOrderDate(deliveryOrderDto.getDeliveryDate());
        if (deliveryOrderDto.getCustomer() != null) {
            String firstName = deliveryOrderDto.getCustomer().getFirstName();
            String lastName = deliveryOrderDto.getCustomer().getLastName();

            String name;
            if (firstName != null && !firstName.isEmpty() && lastName != null && !lastName.isEmpty()) {
                name = firstName + " " + lastName;
            } else if (firstName != null && !firstName.isEmpty()) {
                name = firstName;
            } else if (lastName != null && !lastName.isEmpty()) {
                name = lastName;
            } else {
                name = "";
            }

            generateInvoiceDto.setCustomerDetail(CustomerDetailData.builder()
                .email(deliveryOrderDto.getCustomer().getEmail())
                .fullName(name)
                .build());
        }
        if (deliveryOrderDto.getAddress() != null) {
            generateInvoiceDto.setShippingDetail(AddressDetailData.builder()
                .name(deliveryOrderDto.getAddress().getName())
                .address(
                    deliveryOrderDto.getAddress().getAddressOne())
                .city(deliveryOrderDto.getAddress().getCity())
                .zip(deliveryOrderDto.getAddress().getPostalCode())
                .provinceCode(
                    deliveryOrderDto.getAddress().getState())
                .phoneNumber(deliveryOrderDto.getAddress().getPhone())
                .build());
        }
        generateInvoiceDto.setSummaryData(SummaryData.builder().paid(deliveryOrderDto.getTotalPrice().doubleValue()).build());
        generateInvoiceDto.setLineItems(convert(deliveryOrderDto.getDeliveryOrderItems()));
        generateInvoiceDto.setOrderNote(OrderNoteData.builder().note(deliveryOrderDto.getCustomerNotes()).build());
        generateInvoiceDto.setDriverOrderNote(OrderNoteData.builder().note(deliveryOrderDto.getNotes()).build());
        generateInvoiceDto.setOrganization(OrganizationData.builder().name("Mercaso").build());

        String paymentType = Optional.ofNullable(deliveryOrderDto.getPaymentType())
            .filter(CollectionUtils::isNotEmpty)
            .map(types -> types.stream()
                .map(PaymentType::name)
                .collect(Collectors.joining(",")))
            .orElse(null);
        generateInvoiceDto.setPaymentType(paymentType);
        List<Document> documents = documentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            deliveryOrderDto.getId(),
            EntityEnums.DELIVERY_ORDER.name(),
            List.of(DocumentType.SIGNATURE));
        if (documents != null && !documents.isEmpty()) {
            generateInvoiceDto.setSignature(documentApplicationService.getSingedUrl(documents.getLast().getFileName()));
        }
        DeliveryTask deliveryTask = deliveryTaskRepository.findById(deliveryOrderDto.getDeliveryTaskId());
        if (deliveryTask != null) {
            generateInvoiceDto.setDriverName(deliveryTask.getDriverUserName());
        }
        generateInvoiceDto.setDiscountApplications(deliveryOrderDto.getDiscountApplications());
        return generatePdfService.generateInvoiceAndUpload(generateInvoiceDto);
    }

    List<InvoiceLineItemData> convert(List<DeliveryOrderItemDto> items) {
        return items.stream().map(item -> {
            InvoiceLineItemData itemData = new GenerateInvoiceDto.InvoiceLineItemData();
            itemData.setTitle(item.getTitle());
            itemData.setCasePriceInUsd(item.getPrice() != null ? item.getPrice().doubleValue() : 0.0);
            itemData.setSku(item.getSkuNumber());
            itemData.setQty(item.getDeliveredQty() != null ? item.getDeliveredQty().doubleValue() : 0.0);
            itemData.setOrderedQty(item.getQty().doubleValue());
            itemData.setPositionIdxInCart(item.getLine());
            itemData.setRefundedQty(itemData.getOrderedQty() - itemData.getQty());
            itemData.setAmount(itemData.getQty() * itemData.getCasePriceInUsd());
            itemData.setCrvPerCase(item.getCrvPrice() != null && item.getPackageSize() != null ? item.getCrvPrice().multiply(
                BigDecimal.valueOf(item.getPackageSize())).doubleValue() : 0.0);
            itemData.setFulfillmentStatus(itemData.getOrderedQty() - itemData.getQty() == 0 ? "fulfilled" : "partial");
            itemData.setProductContainsNicotine(item.isContainsNicotine());
            itemData.setDiscountAllocations(item.getDiscountAllocations());
            return itemData;
        }).toList();
    }

}
