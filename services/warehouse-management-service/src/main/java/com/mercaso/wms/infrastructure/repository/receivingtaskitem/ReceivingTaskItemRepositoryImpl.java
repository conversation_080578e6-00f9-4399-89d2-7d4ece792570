package com.mercaso.wms.infrastructure.repository.receivingtaskitem;

import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.receivingtaskitem.jpa.ReceivingTaskItemJpaDao;
import com.mercaso.wms.infrastructure.repository.receivingtaskitem.jpa.dataobject.ReceivingTaskItemDo;
import com.mercaso.wms.infrastructure.repository.receivingtaskitem.jpa.mapper.ReceivingTaskItemDoMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ReceivingTaskItemRepositoryImpl implements ReceivingTaskItemRepository {

    public final ReceivingTaskItemDoMapper mapper;
    private final ReceivingTaskItemJpaDao jpaDao;

    @Override
    public ReceivingTaskItem save(ReceivingTaskItem domain) {
        ReceivingTaskItemDo save = jpaDao.save(mapper.domainToDo(domain));
        return mapper.doToDomain(save);
    }

    @Override
    public ReceivingTaskItem findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public ReceivingTaskItem update(ReceivingTaskItem domain) {
        ReceivingTaskItemDo receivingTaskItemDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == receivingTaskItemDo) {
            throw new WmsBusinessException("ReceivingTaskItems not found.");
        }
        ReceivingTaskItemDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, receivingTaskItemDo, ignoreProperties.toArray(new String[0]));
        return mapper.doToDomain(jpaDao.save(receivingTaskItemDo));
    }

}
