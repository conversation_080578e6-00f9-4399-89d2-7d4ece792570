ALTER table picking_task_items
    add COLUMN IF NOT EXISTS category varchar(100);

create index IF NOT EXISTS picking_task_items_category_idx on picking_task_items (category);

COMMENT ON COLUMN picking_task_items.category IS 'Item category';

update picking_task_items pti
set category = (select category from batch_items bi where bi.id = pti.batch_item_id)
where pti.category is null
  and exists(select 1 from batch_items bi where pti.batch_item_id = bi.id and bi.category is not null);