package com.mercaso.ims;


import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.queryservice.VendorQueryApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.ItemCostChangeRequestApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.enums.BrandStatus;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.CategoryRepository;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.domain.category.service.CategoryService;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.service.CategoryHierarchyService;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecordRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.ItemParetoGrade;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequestRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequestRepository;
import com.mercaso.ims.domain.itemcostchangerequest.service.ItemCostChangeRequestService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollectionRepository;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroupRepository;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPriceRepository;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.testorder.TestOrderRepository;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.domain.vendorpoanalyzerecord.service.VendorPoAnalyzeRecordService;
import com.mercaso.ims.infrastructure.config.ImsAlertConfig;
import com.mercaso.ims.infrastructure.config.SlackConfig;
import com.mercaso.ims.infrastructure.excel.generator.MerchandiseReportExcelGenerator;
import com.mercaso.ims.infrastructure.external.aws_ocr.AwsAnalyzeExpenseAdaptor;
import com.mercaso.ims.infrastructure.external.downey.DowneyAdaptor;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.infrastructure.repository.businessevent.CustomizedBusinessEventJapDao;
import com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa.CustomizedExceptionRecordJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.ItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.ItemAttributeJpaDao;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject.ItemAttributeDo;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.CustomizedItemCostChangeRequestJpaDao;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.CustomizedItemCostCollectionJpaDao;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.ItemGradeJpaDao;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.dataobject.ItemGradeDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.VendorJpaDao;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.VendorItemJpaDao;
import com.mercaso.ims.utils.interfacesutils.*;
import com.mercaso.ims.utils.bulkexportrecords.BulkExportRecordsTestUtil;
import com.mercaso.ims.utils.item.ItemDoUtil;
import com.mercaso.ims.utils.itempricegroup.ItemPriceGroupUtil;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;


@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
})
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EmbeddedKafka(count = 3)
@AutoConfigureMockMvc
public abstract class AbstractIT {


    private final String departmentStr = "department";
    private final String categoryStr = "category";
    private final String subCategoryStr = "subCategory";
    private final String clazzStr = "clazzStr";
    private final String brandNameStr = "brandName";
    private final String vendorNameStr = "vendorName";
    private final String vendorItemNumberStr = "vendorItemNumber";

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected ItemAdjustmentRequestRestApiUtil itemAdjustmentRequestRestApiUtil;
    @Autowired
    protected ItemAdjustmentRequestDetailRepository itemAdjustmentRequestDetailRepository;
    @Autowired
    protected ItemRepository itemRepository;
    @Autowired
    protected ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    @Autowired
    protected ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    @MockBean
    protected DocumentOperations documentOperations;
    @MockBean
    protected FeatureFlagsManager featureFlagsManager;
    @Autowired
    protected MerchandiseReportRestApiUtil merchandiseReportRestApiUtil;

    @Autowired
    protected AttributeService attributeService;
    @Autowired
    protected ItemApplicationService itemApplicationService;

    @Autowired
    protected CustomizedBusinessEventJapDao customizedBusinessEventJapDao;

    @Autowired
    protected ItemAttributeJpaDao attributeJpaDao;

    @MockBean
    protected ShopifyAdaptor shopifyAdaptor;
    @Autowired
    protected SearchItemRequestRestApiUtil searchItemRequestRestApiUtil;
    @Autowired
    protected ItemRestApiUtil itemRestApiUtil;
    @Autowired
    protected SearchCategoryTreeRequestRestApiUtil searchCategoryTreeRequestRestApiUtil;
    @Autowired
    protected ItemAuditHistoryV2RestApiUtil itemAuditHistoryV2RestApiUtil;
    @Autowired
    protected ItemAdjustmentRequestRepository itemAdjustmentRequestRepository;
    @Autowired
    protected QueryItemRestApiUtil queryItemRestApiUtil;
    @Autowired
    protected CategoryApplicationService categoryApplicationService;
    @Autowired
    protected CategoryService categoryService;
    @Autowired
    protected CategoryHierarchyService categoryHierarchyService;
    @Autowired
    protected BrandService brandService;
    @Autowired
    protected ItemJpaDao itemJpaDao;
    @Autowired
    protected VendorJpaDao vendorJpaDao;
    @Autowired
    protected VendorItemJpaDao vendorItemJpaDao;
    @Autowired
    protected CustomizedItemJpaDao customizedItemJpaDao;

    @Autowired
    protected CategoryRestApiUtil categoryRestApiUtil;

    @Autowired
    protected CategoryV2RestApiUtil categoryV2RestApiUtil;

    @MockBean
    protected FinaleAdaptor finaleAdaptor;

    @MockBean
    protected FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @MockBean
    protected MerchandiseReportExcelGenerator excelGenerator;

    @Autowired
    protected QueryVendorRestApiUtil queryVendorRestApiUtil;

    @Autowired
    protected QueryBrandRestApiUtil queryBrandRestApiUtil;

    @Autowired
    protected QueryAttributeRestApiUtil queryAttributeRestApiUtil;
    @Autowired
    protected ItemRegPriceService itemRegPriceService;
    @Autowired
    protected VendorItemService vendorItemService;

    @Autowired
    protected BrandRestApiUtil brandRestApiUtil;

    @Autowired
    protected VendorItemRestApiUtil vendorItemRestApiUtil;

    @MockBean
    protected ImsAlertConfig imsAlertConfig;

    @MockBean
    protected SlackConfig slackConfig;

    @Autowired
    protected VendorService vendorService;

    @Autowired
    protected VendorRestApiUtil vendorRestApiUtil;

    @Autowired
    protected ItemCostChangeRequestRepository itemCostChangeRequestRepository;
    @Autowired
    protected ItemCostCollectionRepository itemCostCollectionRepository;

    @Autowired
    protected ItemPriceRestApiUtil itemPriceRestApiUtil;

    @Autowired
    protected ItemCostCollectionApplicationService itemCostCollectionApplicationService;

    @Autowired
    protected ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService;

    @Autowired
    protected AnalyzeVendorPoRestApiUtil analyzeVendorPoRestApiUtil;

    @MockBean
    protected AwsAnalyzeExpenseAdaptor awsAnalyzeExpenseAdaptor;

    @Autowired
    protected VendorQueryApplicationService vendorQueryApplicationService;

    @Autowired
    protected ItemCostChangeRequestRestApiUtil itemCostChangeRequestRestApiUtil;

    @Autowired
    protected ItemCostChangeRequestService itemCostChangeRequestService;

    @Autowired
    protected VendorPoAnalyzeRecordService vendorPoAnalyzeRecordService;

    @Autowired
    protected CustomizedItemCostCollectionJpaDao customizedItemCostCollectionJpaDao;

    @Autowired
    protected CustomizedItemCostChangeRequestJpaDao customizedItemCostChangeRequestJpaDao;

    @Autowired
    protected ItemPriceGroupRepository itemPriceGroupRepository;

    @Autowired
    protected ItemPriceGroupRestApiUtil itemPriceGroupRestApiUtil;

    @Autowired
    protected ExceptionRecordRepository exceptionRecordRepository;

    @Autowired
    protected ExceptionRecordRestApiUtil exceptionRecordRestApiUtil;

    @MockBean
    protected DowneyAdaptor downeyAdaptor;

    @Autowired
    protected CustomizedExceptionRecordJpaDao customizedExceptionRecordJpaDao;

    @Autowired
    protected QueryItemPriceGroupRestApiUtil queryItemPriceGroupRestApiUtil;

    @Autowired
    protected SearchItemPriceGroupRestApiUtil searchItemPriceGroupRestApiUtil;

    @Autowired
    protected VendorRepository vendorRepository;

    @Autowired
    protected ItemPromoPriceRepository itemPromoPriceRepositoryImpl;
    @Autowired
    protected TestOrderRepository testOrderRepository;

    @Autowired
    protected CategoryRepository categoryRepository;

    @Autowired
    protected SearchExceptionRecordRestApiUtil searchExceptionRecordRestApiUtil;

    @Autowired
    protected SearchItemCostCollectionRestApiUtil searchItemCostCollectionRestApiUtil;

    @Autowired
    protected SearchItemAdjustmentRequestRestApiUtil searchItemAdjustmentRequestRestApiUtil;

    @Autowired
    protected ItemGradeJpaDao itemGradeJpaDao;

    @Autowired
    protected QueryVendorItemRestApiUtil queryVendorItemRestApiUtil;

    @Autowired
    protected SearchCategoryRestApiUtil searchCategoryRestApiUtil;

    @Autowired
    protected SearchBulkExportRecordsRestApiUtil searchBulkExportRecordsRestApiUtil;

    @Autowired
    protected BulkExportRecordsTestUtil bulkExportRecordsTestUtil;

    @Autowired
    protected BulkExportRecordsRestApiUtil bulkExportRecordsRestApiUtil;

    protected ItemDo buildItemData(String skuNumber) {
        return buildItemData(skuNumber,
            departmentStr,
            categoryStr,
            subCategoryStr,
            clazzStr,
            brandNameStr,
            vendorNameStr,
            vendorItemNumberStr, new ArrayList<>());
    }

    protected ItemDo buildItemDataWithVendor(String skuNumber, String vendorName, String vendorItemNumber) {
        return buildItemData(skuNumber,
            departmentStr,
            categoryStr,
            subCategoryStr,
            clazzStr,
            brandNameStr,
            vendorName,
            vendorItemNumber, new ArrayList<>());

    }

    protected ItemDo buildItemData(String skuNumber,
        String department,
        String category,
        String subCategory,
        String clazz,
        String brandName,
        String vendorName,
        String vendorItemNumber) {
        return buildItemData(skuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber, new ArrayList<>());
    }


    protected ItemDo buildItemData(String skuNumber,
        String department,
        String category,
        String subCategory,
        String clazz,
        String brandName,
        String vendorName,
        String vendorItemNumber,
        List<ItemUPCDo> itemUPCs) {

        Brand brand = buildBrandData(brandName);
        UUID brandId = brand.getId();

        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorId = vendorDo.getId();

        ItemDo itemDo = itemJpaDao.findBySkuNumber(skuNumber);
        if (itemDo == null) {
            itemDo = ItemDoUtil.buildItemDo(skuNumber,
                vendorId,
                department,
                category,
                subCategory,
                clazz,
                brandId,
                itemUPCs);
            itemDo = itemJpaDao.save(itemDo);
            ItemGradeDo itemGradeDo = new ItemGradeDo();
            itemGradeDo.setItemId(itemDo.getId().toString());
            itemGradeDo.setSkuNumber(itemDo.getSkuNumber());
            itemGradeDo.setTotalRevenue(BigDecimal.valueOf(100));
            itemGradeDo.setGrade(ItemParetoGrade.A);
            itemGradeJpaDao.save(itemGradeDo);
            buildVendorItemData(vendorItemNumber, vendorId, itemDo.getId());
            buildBottleSizeItemAttributeData(24f, "oz", itemDo.getId());
            buildItemRegPriceData(itemDo.getId());
        }

        return itemDo;
    }

    protected void buildBottleSizeItemAttributeData(Float value, String unit, UUID itemId) {

        ItemAttributeDo attributeDo = new ItemAttributeDo();
        attributeDo.setAttributeId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        attributeDo.setItemId(itemId);
        attributeDo.setValue(value.toString());
        attributeDo.setUnit(unit);
        attributeJpaDao.save(attributeDo);
    }

    protected VendorDo buildVendorDoData(String vendorName) {
        VendorDo vendorDo = vendorJpaDao.findByVendorName(vendorName);
        if (vendorDo == null) {
            vendorDo = VendorUtil.buildVendorDo(vendorName);
            return vendorJpaDao.save(vendorDo);
        }
        return vendorDo;
    }

    protected Brand buildBrandData(String brandName) {
        Brand brand = brandService.findByName(brandName);
        if (null == brand) {
            brand = Brand.builder()
                .name(brandName)
                .logo("")
                .description("")
                .status(BrandStatus.ACTIVE)
                .build();
            brand = brandService.save(brand);
        }
        return brand;
    }

    protected ItemRegPrice buildItemRegPriceData(UUID itemId) {
        ItemRegPrice itemRegPrice = itemRegPriceService.findByItemId(itemId);

        if (null == itemRegPrice) {
            itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(itemId);
            itemRegPrice = itemRegPriceService.save(itemRegPrice);
        }
        return itemRegPrice;
    }

    protected VendorItem buildVendorItemData(String vendorItemNUmber, UUID vendorId, UUID itemId) {
        List<VendorItem> vendorItems = vendorItemService.findByVendorIDAndVendorSkuNum(vendorId, vendorItemNUmber);
        VendorItem vendorItem = null;
        if (vendorItems == null || vendorItems.isEmpty()) {
            vendorItem = VendorItemUtil.buildVendorItem(vendorItemNUmber, vendorId, itemId);
            return vendorItemService.save(vendorItem);
        }
        return vendorItem;
    }

    protected ItemDo buildItemDataWithUpc(String skuNumber, List<ItemUPCDo> upcDos) {
        return buildItemData(skuNumber,
            departmentStr,
            categoryStr,
            subCategoryStr,
            clazzStr,
            brandNameStr,
            vendorNameStr,
            vendorItemNumberStr, upcDos);

    }

    protected Vendor buildVendorData(String vendorName) {
        Vendor vendor = VendorUtil.buildVendor(vendorName);
        return vendorService.save(vendor);
    }


    protected ItemPriceGroup buildItemPriceGroup() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        itemPriceGroup.setId(null);
        return itemPriceGroupRepository.save(itemPriceGroup);
    }

    protected void batchBuildCategory() {
        Category beverage = Category.builder()
            .name("Beverage")
            .status(CategoryStatus.ACTIVE)
            .build();
        beverage = categoryService.save(beverage);
        CategoryHierarchy beverageCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(beverage.getId())
            .depth(0)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(beverageCategoryHierarchy);

        Category soda = Category.builder()
            .name("Soda & Energy Drinks")
            .status(CategoryStatus.ACTIVE)
            .build();
        soda = categoryService.save(soda);
        CategoryHierarchy sodaCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(soda.getId())
            .depth(1)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(sodaCategoryHierarchy);

        Category oatMilk = Category.builder()
            .name("Oat Milk")
            .status(CategoryStatus.ACTIVE)
            .build();
        oatMilk = categoryService.save(oatMilk);
        CategoryHierarchy oatMilkCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(oatMilk.getId())
            .depth(1)
            .sortOrder(2)
            .build();
        categoryHierarchyService.save(oatMilkCategoryHierarchy);

        Category juices = Category.builder()
            .name("Juices & Nectars & Punch")
            .status(CategoryStatus.ACTIVE)
            .build();
        juices = categoryService.save(juices);
        CategoryHierarchy juicesCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(juices.getId())
            .depth(1)
            .sortOrder(3)
            .build();
        categoryHierarchyService.save(juicesCategoryHierarchy);

        Category fruit = Category.builder()
            .name("Fruit Juices")
            .status(CategoryStatus.ACTIVE)
            .build();
        fruit = categoryService.save(fruit);
        CategoryHierarchy fruitJuicesCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(juices.getId())
            .categoryId(fruit.getId())
            .depth(1)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(fruitJuicesCategoryHierarchy);
        CategoryHierarchy fruitBeverageCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(fruit.getId())
            .depth(2)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(fruitBeverageCategoryHierarchy);

        Category alternative = Category.builder()
            .name("Alternative Milk")
            .status(CategoryStatus.ACTIVE)
            .build();
        alternative = categoryService.save(alternative);
        CategoryHierarchy alternativeJuicesCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(juices.getId())
            .categoryId(alternative.getId())
            .depth(1)
            .sortOrder(2)
            .build();
        categoryHierarchyService.save(alternativeJuicesCategoryHierarchy);
        CategoryHierarchy alternativeBeverageCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(alternative.getId())
            .depth(2)
            .sortOrder(2)
            .build();
        categoryHierarchyService.save(alternativeBeverageCategoryHierarchy);

    }

}
