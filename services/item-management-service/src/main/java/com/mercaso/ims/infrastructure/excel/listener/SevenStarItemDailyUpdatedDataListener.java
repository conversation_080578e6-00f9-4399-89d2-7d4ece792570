package com.mercaso.ims.infrastructure.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mercaso.ims.infrastructure.excel.data.SevenStarItemDailyUpdatedData;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SevenStarItemDailyUpdatedDataListener implements ReadListener<SevenStarItemDailyUpdatedData> {

    private List<SevenStarItemDailyUpdatedData> sevenStarItemDailyUpdatedDataList;


    public SevenStarItemDailyUpdatedDataListener(List<SevenStarItemDailyUpdatedData> sevenStarItemDailyUpdatedDataList) {

        this.sevenStarItemDailyUpdatedDataList = sevenStarItemDailyUpdatedDataList;
    }

    @Override
    public void invoke(SevenStarItemDailyUpdatedData data, AnalysisContext analysisContext) {
        try {
            if (data.getItemCode() == null || data.getInnrPrice() == null) {
                return;
            }
            sevenStarItemDailyUpdatedDataList.add(data);
        } catch (Exception e) {
            log.error("create SevenStarItemDailyUpdatedData error", e);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("All data is read successfully for 7 Star");
    }


}