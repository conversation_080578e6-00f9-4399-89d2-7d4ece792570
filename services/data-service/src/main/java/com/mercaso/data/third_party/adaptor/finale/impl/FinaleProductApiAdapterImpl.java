package com.mercaso.data.third_party.adaptor.finale.impl;

import com.mercaso.data.third_party.adaptor.finale.FinaleProductApiAdapter;
import com.mercaso.data.third_party.config.FinaleConfigProperties;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityPrimaryDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleProductDataResponseDto;
import com.mercaso.data.utils.SerializationUtils;
import com.mercaso.data.utils.HttpClientUtils;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class FinaleProductApiAdapterImpl implements FinaleProductApiAdapter {

    private final FinaleConfigProperties properties;


    private static final String AUTH_HEADER = "Basic ";


    public Optional<FinaleProductDataResponseDto> fetchProductData(String facilityUrl) {
        try {
          
            String jsonBody = buildRequestBody(facilityUrl);

            Map<String, String> headers = buildHeaders();

            FinaleProductDataResponseDto finaleProductDataResponseDto = HttpClientUtils.executePostRequest(properties.getGraphqlApiUrl(),
                jsonBody,
                headers,
                FinaleProductDataResponseDto.class);
            return Optional.of(finaleProductDataResponseDto);
        } catch (IOException e) {
            log.warn("Error fetching product data from Finale Inventory API: {}", e.getMessage(), e);
        }

        return Optional.empty();
    }

    @Override
    public Optional<FinaleFacilityDataResponseDto> fetchFacilityData() {
        try {
            Map<String, String> headers = buildHeaders();
            return Optional.ofNullable(HttpClientUtils.executeGetRequest(
                properties.getFacilityApiUrl(),
                headers,
                FinaleFacilityDataResponseDto.class
            ));
        } catch (IOException e) {
            log.warn("Error fetching or parsing facility data from Finale Inventory API: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<FinaleFacilityPrimaryDataResponseDto> fetchFacilityUrlData() {
        try {
            Map<String, String> headers = buildHeaders();

            FinaleFacilityPrimaryDataResponseDto response = HttpClientUtils.executeGetRequest(
                properties.getFinalePrimaryApiUrl(),
                headers,
                FinaleFacilityPrimaryDataResponseDto.class
            );

            log.info("Successfully fetched facility config data from Finale Inventory API");
            return Optional.ofNullable(response);
        } catch (Exception e) {
            log.error("Error fetching facility config data from Finale Inventory API: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    private String buildRequestBody(String facilityUrl){

        Map<String, Object> stringRequestBody = getStringRequestBody();
        String queryString = (String) stringRequestBody.get("query");

        queryString = queryString.replace("facilityUrlList: \"\"", "facilityUrlList: \"" + facilityUrl + "\"");
        queryString = queryString.replace("facilityUrlList: [\"\"]", "facilityUrlList: [\"" + facilityUrl + "\"]");


        stringRequestBody.put("query", queryString);

        return SerializationUtils.serialize(stringRequestBody);
    }

    private Map<String, String> buildHeaders() {
        return Map.of(
            "Content-Type", "application/json",
            "Authorization", AUTH_HEADER + properties.getToken()
        );
    }

    private Map<String,Object> getStringRequestBody(){
        Map<String, Object> map = new HashMap<>();
        map.put("operationName","Rows");
        map.put("variables",Map.of("first", 3000
        ));
        map.put("query","""
            query Rows($after: String, $first: Int) {
              productViewConnection(status: ["PRODUCT_ACTIVE"], stock: {minExclusive: 0, count: totalUnits, stockType: onHand, facilityUrlList: [""]}, after: $after, first: $first, sort: [{field: "productId", mode: "asc"}]) {
                summary {
                  metrics {
                    count
                  }
                }
                edges {
                  node(timezone: "") {
                    name: description
                    stockItemsOnHand: stockItem(type: "STOCK_ITEM_ON_HAND", first: 40) {
                      edges {
                        node {
                          quantityOnHand: quantity(formatter: "none")
                          sublocation {
                            facilityUrl
                            name
                          }
                        }
                      }
                      summary {
                        metrics {
                          count
                        }
                      }
                    }
                    mfcQoh: stockOnHand(count: openQuantity, facilityUrlList: "")
                    sku: productId
                    reservationsQoh: stockReservationsUnits
                    recordLastUpdated: recordLastUpdated
                    stockSublocations: stockSublocations
                    productUrl: productUrl
                  }
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
            }
            """);

        return map;
    }
}