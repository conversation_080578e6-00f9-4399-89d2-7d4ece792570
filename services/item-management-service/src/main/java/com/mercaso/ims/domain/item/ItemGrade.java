package com.mercaso.ims.domain.item;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.ValueObject;
import com.mercaso.ims.domain.item.enums.ItemParetoGrade;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.EqualsBuilder;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ItemGrade extends BaseDomain implements ValueObject<ItemGrade> {

    private UUID id;

    private String itemId;

    private String skuNumber;

    private BigDecimal totalRevenue;

    private ItemParetoGrade grade;


    @Override
    public boolean sameValueAs(ItemGrade other) {
        return other != null && new EqualsBuilder()
            .append(itemId, other.itemId)
            .append(grade, other.grade)
            .isEquals();
    }
}
