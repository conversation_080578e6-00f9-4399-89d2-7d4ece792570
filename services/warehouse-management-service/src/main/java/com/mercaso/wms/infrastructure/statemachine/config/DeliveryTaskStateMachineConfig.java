package com.mercaso.wms.infrastructure.statemachine.config;

import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskTransitionEvents;
import com.mercaso.wms.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.wms.infrastructure.statemachine.factory.WmsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

@Configuration
@EnableStateMachineFactory(contextEvents = false, name = "deliveryTaskTransitionEventsStateMachineFactory")
public class DeliveryTaskStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<DeliveryTaskStatus, DeliveryTaskTransitionEvents> {

    @Override
    public void configure(StateMachineStateConfigurer<DeliveryTaskStatus, DeliveryTaskTransitionEvents> states)
        throws Exception {
        states
            .withStates()
            .initial(DeliveryTaskStatus.CREATED)
            .states(EnumSet.allOf(DeliveryTaskStatus.class))
        ;
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<DeliveryTaskStatus, DeliveryTaskTransitionEvents> transitions)
        throws Exception {
        transitions
            .withExternal()
            .source(DeliveryTaskStatus.CREATED).target(DeliveryTaskStatus.IN_PROGRESS)
            .event(DeliveryTaskTransitionEvents.MARK_AS_IN_PROGRESS)
            .and()
            .withExternal()
            .source(DeliveryTaskStatus.IN_PROGRESS).target(DeliveryTaskStatus.COMPLETED)
            .event(DeliveryTaskTransitionEvents.MARK_AS_COMPLETED);
    }

    @Bean
    @StatemachineFactory(domainClass = DeliveryTask.class)
    public WmsStateMachineFactory<DeliveryTaskStatus, DeliveryTaskTransitionEvents, DeliveryTask> deliveryTaskStateMachineAdapter(
        StateMachineFactory<DeliveryTaskStatus, DeliveryTaskTransitionEvents> DeliveryTaskTransitionEventsStateMachineFactory,
        StateMachinePersister<DeliveryTaskStatus, DeliveryTaskTransitionEvents, DeliveryTask> deliveryTaskStateMachinePersist) {
        return new WmsStateMachineFactory<>(DeliveryTaskTransitionEventsStateMachineFactory, deliveryTaskStateMachinePersist);
    }

    @Bean
    public StateMachinePersister<DeliveryTaskStatus, DeliveryTaskTransitionEvents, DeliveryTask> deliveryTaskrStateMachinePersister(
        StateMachinePersist<DeliveryTaskStatus, DeliveryTaskTransitionEvents, DeliveryTask> deliveryTaskStateMachinePersist) {
        return new DefaultStateMachinePersister<>(deliveryTaskStateMachinePersist);
    }

}
