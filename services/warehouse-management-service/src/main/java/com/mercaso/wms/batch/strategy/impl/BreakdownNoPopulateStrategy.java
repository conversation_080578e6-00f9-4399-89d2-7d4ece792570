package com.mercaso.wms.batch.strategy.impl;

import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.IgnoredOrderDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.strategy.PopulateBatchTemplateStrategy;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Order(4)
@Slf4j
public class BreakdownNoPopulateStrategy implements PopulateBatchTemplateStrategy {

    public static final List<String> ignoredLocations = List.of("G-27", "G-28", "G-29", "G-30", "G-31", "G-32", "G-33", "G-34");

    @Override
    public List<ExcelBatchDto> populateBatchTemplate(PopulateCondition populateCondition) {
        log.info("[BreakdownNoPopulateStrategy] Start to populate batch template");
        List<ExcelBatchDto> excelBatchDtoList = populateCondition.getExcelBatchDtoList();
        if (CollectionUtils.isEmpty(excelBatchDtoList)) {
            log.error("[BreakdownNoPopulateStrategy] BatchDtoList is empty");
            return List.of();
        }

        validateIgnoreOrders(populateCondition);

        List<Location> mdcBigShippingLocations = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(populateCondition.getLocations())) {
            getMdcLocations(populateCondition, mdcBigShippingLocations);
        }

        Map<String, List<ExcelBatchDto>> groupedByOrderNumber = getExcelBatchGroupedByQuantity(excelBatchDtoList);

        assignBreakdownNumber(
            groupedByOrderNumber,
            mdcBigShippingLocations);

        excelBatchDtoList.sort(Comparator.comparing(ExcelBatchDto::getOrderNumber).thenComparing(ExcelBatchDto::getLine));
        log.info("[BreakdownNoPopulateStrategy] Finish to populate batch template");
        return excelBatchDtoList;
    }

    private void assignBreakdownNumber(Map<String, List<ExcelBatchDto>> groupedByQuantity,
        List<Location> mdcBigShippingLocations) {
        if (CollectionUtils.isEmpty(mdcBigShippingLocations)) {
            log.error("[BreakdownNoPopulateStrategy] Big shipping locations is empty");
            return;
        }
        if (groupedByQuantity != null) {
            groupedByQuantity.forEach((orderNumber, orders) -> assignMDCBreakdownNumber(orders, mdcBigShippingLocations));
        }
    }

    public Map<String, List<ExcelBatchDto>> getExcelBatchGroupedByQuantity(List<ExcelBatchDto> excelBatchDtoList) {
        LinkedHashMap<String, List<ExcelBatchDto>> groupedBatchMap = excelBatchDtoList.stream()
            .sorted(Comparator.comparing(ExcelBatchDto::getDistrictName, Comparator.nullsLast(String::compareTo)))
            .collect(Collectors.groupingBy(
                ExcelBatchDto::getOrderNumber,
                LinkedHashMap::new,
                Collectors.toList()
            ));
        for (String orderNumber : groupedBatchMap.keySet()) {
            List<ExcelBatchDto> excelBatchDtos = groupedBatchMap.get(orderNumber);
            List<ExcelBatchDto> mdcItems = excelBatchDtos.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getSource()) && item.getSource().equals(SourceEnum.MDC.name()))
                .filter(item -> StringUtils.isNotEmpty(item.getFrom()) && !item.getFrom().equals("PHOTO-STUDIO"))
                .toList();
            if (!CollectionUtils.isEmpty(mdcItems)) {
                int sum = mdcItems.stream().mapToInt(ExcelBatchDto::getQuantity).sum();
                if (sum > 5) {
                    mdcItems.forEach(excelBatchDto -> excelBatchDto.setBigOrder(true));
                }
            }
        }
        return groupedBatchMap;
    }

    private void assignMDCBreakdownNumber(List<ExcelBatchDto> order, List<Location> mdcShippingLocations) {
        Location location = mdcShippingLocations.getFirst();
        order.forEach(batchDto -> {
            batchDto.setPos(location.getName());
            batchDto.setBreakdownLocationId(location.getId());
        });
        mdcShippingLocations.removeFirst();
    }

    private void getMdcLocations(PopulateCondition populateCondition, List<Location> mdcShippingLocations) {
        populateCondition.getLocations().stream()
            .filter(location -> SourceEnum.MDC.name().equals(location.getWarehouse().getName()))
            .filter(location -> location.getType() == LocationType.SHIPPING_BIG)
            .filter(location -> StringUtils.isNotEmpty(location.getName()) &&
                !ignoredLocations.contains(location.getName()))
            .filter(location -> CollectionUtils.isEmpty(populateCondition.getShippingOrders()) ||
                populateCondition.getIgnoredOrders().stream()
                    .map(IgnoredOrderDto::getBreakdown)
                    .noneMatch(location.getName()::equals))
            .sorted(Comparator.comparing(Location::getName))
            .forEach(mdcShippingLocations::add);
    }

    private void validateIgnoreOrders(PopulateCondition populateCondition) {
        if (CollectionUtils.isEmpty(populateCondition.getIgnoredOrders())) {
            return;
        }
        populateCondition.getIgnoredOrders().forEach(ignoredOrderDto -> {
            if (StringUtils.isEmpty(ignoredOrderDto.getBreakdown())) {
                throw new WmsBusinessException("Breakdown number is empty for ignored order " + ignoredOrderDto.getOrderNumber());
            }
        });
    }

}