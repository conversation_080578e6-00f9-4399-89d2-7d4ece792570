package com.mercaso.wms.infrastructure.config;

import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.exception.SingleDeviceLoginException;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.Instant;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.statemachine.StateMachineException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

@RestControllerAdvice
@Slf4j
public class ControllerExceptionHandler {

    /**
     * Handler for WmsBusinessException. Maps to HTTP 400 (bad request)
     */
    @ExceptionHandler(value = WmsBusinessException.class)
    public ResponseEntity<ErrorDTO> handleException(WmsBusinessException ex, HttpServletRequest request) {

        log.warn("WmsBusinessException, path= {}, httpCode = 422, error message: {}, error details: ",
            request.getRequestURI(),
            ex.getMessage(),
            ex);
        return createResponseEntity(ex.getMessage(), ex.getCode(), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorDTO> handleException(HttpMessageNotReadableException ex) {
        log.warn(ex.getMessage(), ex);
        return createResponseEntity(ex.getMessage(),
            HttpStatus.BAD_REQUEST.toString(),
            HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<ErrorDTO> handleException(Exception ex, HttpServletRequest request) {

        log.error("Exception, path= {}, httpCode = 500, error message: {}, error details: ",
            request.getRequestURI(),
            ex.getMessage(),
            ex);
        return createResponseEntity(ex.getMessage(),
            HttpStatus.INTERNAL_SERVER_ERROR.toString(),
            HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = AuthorizationDeniedException.class)
    public ResponseEntity<ErrorDTO> handleAuthorizationDeniedException(AuthorizationDeniedException ex,
        HttpServletRequest request) {

        log.warn("AuthorizationDeniedException, path= {}, httpCode = 403, error message: {}, error details: ",
            request.getRequestURI(),
            ex.getMessage(),
            ex);
        return createResponseEntity(ex.getMessage(),
            HttpStatus.FORBIDDEN.toString(),
            HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorDTO> handleException(MethodArgumentTypeMismatchException ex) {
        return createResponseEntity(ex.getMessage(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = DeliveryBusinessException.class)
    public ResponseEntity<ErrorDTO> handleException(DeliveryBusinessException ex,
        HttpServletRequest request) {

        log.warn("DeliveryBusinessException, path= {}, httpCode = 422, error message: {}, error details: ",
            request.getRequestURI(),
            ex.getMessage(),
            ex);
        return createResponseEntity(ex.getMessage(), ex.getCode(), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @ExceptionHandler(value = DeliveryBadRequestException.class)
    public ResponseEntity<ErrorDTO> handleException(DeliveryBadRequestException ex,
        HttpServletRequest request) {

        log.warn("DeliveryBadRequestException, path= {}, httpCode = 400, error message: {}, error details: ",
            request.getRequestURI(),
            ex.getMessage(),
            ex);
        return createResponseEntity(ex.getMessage(),
            HttpStatus.UNPROCESSABLE_ENTITY.toString(),
            HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = BindException.class)
    public ResponseEntity<ErrorDTO> handleException(BindException ex) {

        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
            .map(fieldError -> fieldError.getDefaultMessage() != null ?
                fieldError.getDefaultMessage() : "Bad request, invalid field")
            .findFirst()
            .orElse(ex.getMessage());

        return createResponseEntity(errorMessage,
            HttpStatus.BAD_REQUEST.toString(),
            HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = {StateMachineException.class, IOException.class})
    public ResponseEntity<ErrorDTO> handleException(StateMachineException ex) {
        return createResponseEntity(ex.getMessage(), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity<ErrorDTO> handleException(IllegalArgumentException ex) {
        return createResponseEntity(ex.getMessage(), HttpStatus.BAD_REQUEST);
    }

    /**
     * Handler for the MissingServletRequestParameterException.class exception.
     */
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorDTO> handleException(MissingServletRequestParameterException e) {
        return createResponseEntity(e.getMessage(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(SingleDeviceLoginException.class)
    public ResponseEntity<ErrorDTO> handleSingleDeviceLoginException(SingleDeviceLoginException ex,
        HttpServletRequest request) {

        log.warn("SingleDeviceLoginException, path= {}, httpCode = 401, error message: {}, error details: ",
            request.getRequestURI(),
            ex.getMessage(),
            ex);
        return createResponseEntity(ex.getMessage(), HttpStatus.UNAUTHORIZED.getReasonPhrase(), HttpStatus.UNAUTHORIZED);
    }

    /**
     * Create a ResponseEntity from a code, message and desired HttpStatus.
     */
    protected static ResponseEntity<ErrorDTO> createResponseEntity(String message, String businessCode, HttpStatus httpStatus) {
        return new ResponseEntity<>(buildResponse(message, businessCode, httpStatus), httpStatus);
    }

    protected static ResponseEntity<ErrorDTO> createResponseEntity(String message, HttpStatus httpStatus) {
        return new ResponseEntity<>(buildResponse(message, null, httpStatus), httpStatus);
    }

    /**
     * Creates an ErrorDetail object from code and message (for now).
     */
    protected static ErrorDTO buildResponse(String message, String businessCode, HttpStatus httpStatus) {
        ErrorDTO result = new ErrorDTO();
        result.setCode(httpStatus.value());
        result.setBusinessCode(businessCode);
        result.setMessage(message);
        result.setTimestamp(Objects.toString(Instant.now()));
        return result;
    }

}
