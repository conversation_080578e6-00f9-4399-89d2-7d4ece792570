package com.mercaso.wms.infrastructure.utils;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

public class DateUtils {

    private DateUtils() {
    }

    public static final ZoneId LA_ZONE = ZoneId.of("America/Los_Angeles");

    public static final DateTimeFormatter DATE_TO_STRING_FORMATTER = DateTimeFormatter.ISO_DATE;
    public static final DateTimeFormatter RM_DATE_TO_STRING_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static String laDateTime() {
        return ZonedDateTime.now(LA_ZONE).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    public static boolean isSaturday(LocalDate date) {
        return date.getDayOfWeek() == DayOfWeek.SATURDAY;
    }

    public static LocalDate getNextDeliveryDate() {
        LocalDate losAngelesDate = Instant.now().atZone(DateUtils.LA_ZONE).toLocalDate();
        if (DateUtils.isSaturday(losAngelesDate)) {
            losAngelesDate = losAngelesDate.plusDays(2);
        } else {
            losAngelesDate = losAngelesDate.plusDays(1);
        }
        return losAngelesDate;
    }

    public static Instant getStartOfDayInLA() {
        LocalDate todayInLA = LocalDate.now(LA_ZONE);
        ZonedDateTime startOfDayInLA = todayInLA.atStartOfDay(LA_ZONE);
        return startOfDayInLA.toInstant();
    }

    public static Instant getNowInLA() {
        return ZonedDateTime.now(LA_ZONE).toInstant();
    }

    public static Instant getEndOfDayInLA() {
        LocalDate todayInLA = LocalDate.now(LA_ZONE);
        ZonedDateTime endOfDayInLA = todayInLA.plusDays(1).atStartOfDay(LA_ZONE).minusNanos(1);
        return endOfDayInLA.toInstant();
    }

    public static Instant secondsSinceMidnight(String date, int sec) {
        return Optional.ofNullable(date)
            .map(d -> LocalDate.parse(d, DATE_TO_STRING_FORMATTER)
                .atStartOfDay(LA_ZONE)
                .plusSeconds(sec)
                .toInstant())
            .orElseGet(() -> ZonedDateTime.now(LA_ZONE)
                .truncatedTo(ChronoUnit.DAYS)
                .plusSeconds(sec)
                .toInstant());
    }

    public static int secondsSinceMidnightTo(Instant instant) {
        ZonedDateTime zonedDateTime = instant.atZone(LA_ZONE);
        ZonedDateTime midnight = zonedDateTime.truncatedTo(ChronoUnit.DAYS);
        return (int) ChronoUnit.SECONDS.between(midnight, zonedDateTime);
    }
}
