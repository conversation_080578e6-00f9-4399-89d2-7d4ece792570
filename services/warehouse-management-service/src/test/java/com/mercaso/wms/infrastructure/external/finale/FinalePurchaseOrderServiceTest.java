package com.mercaso.wms.infrastructure.external.finale;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildVendorDtoList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.VendorDto;
import com.mercaso.ims.client.dto.VendorItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.PurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.ReceivePurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import java.math.BigDecimal;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.List;
import java.util.UUID;

class FinalePurchaseOrderServiceTest {

    @Mock
    private FinaleProductService finaleProductService;

    @Mock
    private PickingTaskRepository pickingTaskRepository;

    @Mock
    private ReceivingTaskRepository receivingTaskRepository;

    @Mock
    private ImsAdaptor imsAdaptor;

    @Mock
    private FinaleConfigProperties finaleConfigProperties;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private LocationRepository locationRepository;

    @InjectMocks
    private FinalePurchaseOrderService finalePurchaseOrderService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createPurchaseOrder_CreatesAndProcesses_For_OnlineSupplier() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);
        // Mission is the online supplier
        SourceEnum source = SourceEnum.MISSION;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();
        List<ItemCategoryDto> itemCategoryDtos = new ArrayList<>();
        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId, 1, source, ReceivingTaskStatus.RECEIVED,
            ReceivingTaskType.ONLINE_RECEIVING);
        receivingTasks.getFirst().getReceivingTaskItems().forEach(receivingTaskItem -> {
            ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
            VendorItemDto vendorItemDto = new VendorItemDto();
            vendorItemDto.setVendorId(vendorDto.getId());
            vendorItemDto.setVendorName(vendorDto.getVendorName());
            vendorItemDto.setBackupCost(new BigDecimal("100.1"));
            vendorItemDto.setItemId(receivingTaskItem.getItemId());
            vendorItemDto.setItemSkuNumber(receivingTaskItem.getSkuNumber());

            itemCategoryDto.setId(receivingTaskItem.getItemId());
            itemCategoryDto.setSkuNumber(receivingTaskItem.getSkuNumber());
            itemCategoryDto.setVendorItemDtos(List.of(vendorItemDto));
            itemCategoryDtos.add(itemCategoryDto);
        });

        PurchaseOrderResponse response = new PurchaseOrderResponse();
        response.setOrderId("123456");
        List<PurchaseOrderResponse.OrderItem> orderItems = new ArrayList<>();
        PurchaseOrderResponse.OrderItem orderItem = new PurchaseOrderResponse.OrderItem();
        orderItem.setOrderItemUrl("orderItemUrl");
        orderItem.setProductId("orderItemId");
        orderItem.setQuantity(1);
        response.setOrderItemList(orderItems);

        ReceivePurchaseOrderResponse receivePurchaseOrderResponse = new ReceivePurchaseOrderResponse();
        receivePurchaseOrderResponse.setShipmentId("123456");

        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(receivingTasks);
        when(imsAdaptor.getVendorByName(anyString())).thenReturn(vendorDto);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(itemCategoryDtos);
        when(finaleProductService.createPurchaseOrder(any())).thenReturn(response);
        when(finaleProductService.receivePurchaseOrder(any())).thenReturn(receivePurchaseOrderResponse);
        when(finaleProductService.completePurchaseOrder(any())).thenReturn(response);
        when(batchRepository.findById(batchId)).thenReturn(batch);
        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verify(finaleProductService, times(1)).createPurchaseOrder(any());
    }

    @Test
    void createPurchaseOrder_CreatesAndProcesses_For_offlineSupplier() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);
        // Mission is the online supplier
        SourceEnum source = SourceEnum.COSTCO;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();
        List<ItemCategoryDto> itemCategoryDtos = new ArrayList<>();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1, source, PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);
        pickingTasks.getFirst().getPickingTaskItems().forEach(pickingTaskItem -> {
            ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
            VendorItemDto vendorItemDto = new VendorItemDto();
            vendorItemDto.setVendorId(vendorDto.getId());
            vendorItemDto.setVendorName(vendorDto.getVendorName());
            vendorItemDto.setBackupCost(new BigDecimal("100.1"));
            vendorItemDto.setItemId(pickingTaskItem.getItemId());
            vendorItemDto.setItemSkuNumber(pickingTaskItem.getSkuNumber());

            itemCategoryDto.setId(pickingTaskItem.getItemId());
            itemCategoryDto.setSkuNumber(pickingTaskItem.getSkuNumber());
            itemCategoryDto.setVendorItemDtos(List.of(vendorItemDto));
            itemCategoryDtos.add(itemCategoryDto);
        });

        PurchaseOrderResponse response = new PurchaseOrderResponse();
        response.setOrderId("123456");
        List<PurchaseOrderResponse.OrderItem> orderItems = new ArrayList<>();
        PurchaseOrderResponse.OrderItem orderItem = new PurchaseOrderResponse.OrderItem();
        orderItem.setOrderItemUrl("orderItemUrl");
        orderItem.setProductId("orderItemId");
        orderItem.setQuantity(1);
        response.setOrderItemList(orderItems);

        ReceivePurchaseOrderResponse receivePurchaseOrderResponse = new ReceivePurchaseOrderResponse();
        receivePurchaseOrderResponse.setShipmentId("123456");

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(pickingTasks);
        when(imsAdaptor.getVendorByName(anyString())).thenReturn(vendorDto);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(itemCategoryDtos);
        when(finaleProductService.createPurchaseOrder(any())).thenReturn(response);
        when(finaleProductService.receivePurchaseOrder(any())).thenReturn(receivePurchaseOrderResponse);
        when(finaleProductService.completePurchaseOrder(any())).thenReturn(response);
        when(batchRepository.findById(batchId)).thenReturn(batch);
        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verify(finaleProductService, times(1)).createPurchaseOrder(any());
    }

    @Test
    void createPurchaseOrder_LogsErrorWhenVendorNotFound() {
        SourceEnum source = mock(SourceEnum.class);
        Batch batch = mock(Batch.class);

        when(source.getVendorName()).thenReturn("NonExistentVendor");
        when(imsAdaptor.getVendorByName("NonExistentVendor")).thenReturn(null);

        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verifyNoInteractions(finaleProductService);
    }

    @Test
    void processReceivingTasks_Returns_Null_When_NoTasksExist() {
        UUID batchId = UUID.randomUUID();
        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(List.of());

        PurchaseOrderResponse response = finalePurchaseOrderService.processReceivingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", mock(SourceEnum.class));

        assertNull(response);
    }

    @Test
    void processPickingTasksReturns_Null_When_NoTasksExist() {
        UUID batchId = UUID.randomUUID();
        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of());

        PurchaseOrderResponse response = finalePurchaseOrderService.processPickingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", mock(SourceEnum.class));

        assertNull(response);
    }

}