package com.mercaso.data.master_catalog.dto.dashboard;

import com.mercaso.data.master_catalog.dto.BaseDto;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class InventoryStockTrendDto extends BaseDto {

    private Instant date;
    private int quantity;
}
