package com.mercaso.ims.interfaces.rest;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.service.BulkExportRecordsApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.UUID;

@RestController
@RequestMapping(value = "/v1/bulk-export-records")
@RequiredArgsConstructor
@Slf4j
public class BulkExportRecordsRestApi {

    private final BulkExportRecordsApplicationService bulkExportRecordsApplicationService;

    @GetMapping("/{id}/file")
    @PreAuthorize("hasAuthority('ims:read:item-adjustment-requests')")
    public DocumentResponse getBulkExportRecordsFile(@PathVariable UUID id) {
        return bulkExportRecordsApplicationService.getBulkExportRecordsFile(id);
    }
}


