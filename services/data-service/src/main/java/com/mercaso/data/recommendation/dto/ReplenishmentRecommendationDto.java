package com.mercaso.data.recommendation.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ReplenishmentRecommendationDto {

  private UUID storeId;
  private String sku;
  private String name;
  private BigDecimal recommendedQuantity;
  private Instant nextOrderTime;
  private String batchNumber;
  private String upc;
  private String productId;
  private Instant lastPurchaseTime;
  private Integer lastPurchaseQuantity;
  private UUID departmentId;
  private String departmentName;
}