package com.mercaso.data.master_catalog.utils.resource_utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.Map;
import java.util.UUID;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Component
public class MasterCatalogRawDataResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V1_RAW_DATA = "/master-catalog/v1/raw-data";
    public static final String V1_RAW_DATA_SEARCH = "/master-catalog/v1/raw-data/search";
    public static final String V1_RAW_DATA_SEARCH_BY_IMAGE = "/master-catalog/v1/raw-data/search-by-image";
    public static final String V1_RAW_DATA_SEARCH_DUPLICATION = "/master-catalog/v1/raw-data/duplication/search";

    public MasterCatalogRawDataResourceApiUtils(Environment environment) {
        super(environment);
    }

    public CustomPage<MasterCatalogRawDataDto> getMasterCatalogRawData() {

        Map<String, String> params = new java.util.HashMap<>(Map.of());
        params.put("page", "1");
        params.put("pageSize", "2");

        ParameterizedTypeReference<CustomPage<MasterCatalogRawDataDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(V1_RAW_DATA_SEARCH, responseType, params).getBody();
    }

    public CustomPage<MasterCatalogRawDataDto> searchMasterCatalogRawDataByImage() throws JsonProcessingException {
        byte[] imageContent = "test image content".getBytes();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new ByteArrayResource(imageContent) {
            @Override
            public String getFilename() {
                return "test.jpg";
            }
        });

        String jsonResponse = objectMapper.writeValueAsString(postEntity(V1_RAW_DATA_SEARCH_BY_IMAGE + "?page=1&&pageSize=2",
            body,
            CustomPage.class,
            MediaType.MULTIPART_FORM_DATA).getBody());

        return objectMapper.readValue(
            jsonResponse,
            new TypeReference<>() {
            }
        );
    }

    public MasterCatalogRawDataDto getMasterCatalogRawDataById(UUID id) {
        return getForObject(V1_RAW_DATA + "/" + id, MasterCatalogRawDataDto.class);
    }

    public PageableResponse<MasterCatalogRawDataDto> getMasterCatalogDuplicationRawData(String upc) throws JsonProcessingException {

        Map<String, String> params = new java.util.HashMap<>(Map.of());
        params.put("page", "1");
        params.put("pageSize", "20");
        params.put("upc", upc);

        ResponseEntity<String> response = performRequest(
                V1_RAW_DATA_SEARCH_DUPLICATION,
                params,
                null,
                HttpMethod.GET);

        return objectMapper.readValue(response.getBody(),
                objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
                        MasterCatalogRawDataDto.class));
    }
}
