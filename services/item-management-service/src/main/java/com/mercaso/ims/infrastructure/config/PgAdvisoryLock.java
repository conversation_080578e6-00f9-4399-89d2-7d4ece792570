package com.mercaso.ims.infrastructure.config;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Slf4j
public class PgAdvisoryLock {

    private static final String LOCK_KEY = "lockKey";
    private final EntityManager entityManager;

    @Transactional(propagation = Propagation.MANDATORY)
    public void tryAcquireTransactionalLevelAdvisoryLock(Integer lockKey, String description) {
        log.debug("try to acquire advisory lock lockKey: {}, methodName: {}", lockKey, description);

        String sql = "SELECT count(*) from pg_advisory_xact_lock(:lockKey)";

        Query q = entityManager.createNativeQuery(sql);
        q.setParameter(LOCK_KEY, lockKey);

        q.getSingleResult();
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public Boolean tryLockWithTransactionLevel(Integer lockKey, String description) {
        log.debug("try advisory lock lockKey: {}, methodName: {}", lockKey, description);

        String sql = "SELECT pg_try_advisory_xact_lock(:lockKey)";
        Query q = entityManager.createNativeQuery(sql);
        q.setParameter(LOCK_KEY, lockKey);
        return (Boolean) q.getSingleResult();
    }


    public Boolean tryLockWithSessionLevel(EntityManager entityManager, Integer lockKey, String description) {
        log.debug("pg_try_advisory_lock lockKey: {}, methodName: {}", lockKey, description);

        String sql = "SELECT pg_try_advisory_lock(:lockKey)";
        Query q = entityManager.createNativeQuery(sql);
        q.setParameter(LOCK_KEY, lockKey);
        return (Boolean) q.getSingleResult();
    }

    public Boolean unLock(EntityManager entityManager, Integer lockKey, String description) {
        log.debug("unLock lock lockKey: {}, methodName: {}", lockKey, description);

        String sql = "SELECT pg_advisory_unlock(:lockKey)";
        Query q = entityManager.createNativeQuery(sql);
        q.setParameter(LOCK_KEY, lockKey);
        return (Boolean) q.getSingleResult();
    }


}
